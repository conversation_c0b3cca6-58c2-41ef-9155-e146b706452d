import com.fasterxml.jackson.databind.ObjectMapper;
import cn.need.cloud.biz.client.constant.enums.base.ShipTypeEnum;

public class EnumNullHandlingTest {
    public static void main(String[] args) {
        try {
            ObjectMapper mapper = new ObjectMapper();
            
            // 测试正常值
            System.out.println("=== 测试正常值 ===");
            String normalJson = "\"ByRequestRoutingInstructionFile\"";
            ShipTypeEnum normalEnum = mapper.readValue(normalJson, ShipTypeEnum.class);
            System.out.println("正常值: " + normalEnum);
            
            // 测试 null 值
            System.out.println("\n=== 测试 null 值 ===");
            String nullJson = "null";
            ShipTypeEnum nullEnum = mapper.readValue(nullJson, ShipTypeEnum.class);
            System.out.println("null值: " + nullEnum);
            
            // 测试空字符串
            System.out.println("\n=== 测试空字符串 ===");
            String emptyJson = "\"\"";
            ShipTypeEnum emptyEnum = mapper.readValue(emptyJson, ShipTypeEnum.class);
            System.out.println("空字符串: " + emptyEnum);
            
            // 测试只有空格的字符串
            System.out.println("\n=== 测试空格字符串 ===");
            String spaceJson = "\"   \"";
            ShipTypeEnum spaceEnum = mapper.readValue(spaceJson, ShipTypeEnum.class);
            System.out.println("空格字符串: " + spaceEnum);
            
            // 测试完整的JSON对象
            System.out.println("\n=== 测试完整JSON对象 ===");
            String objectJson = "{\"shipType\": \"\", \"note\": \"test\"}";
            TestObject testObj = mapper.readValue(objectJson, TestObject.class);
            System.out.println("对象中的枚举: " + testObj.shipType);
            
            System.out.println("\n所有测试通过！");
            
        } catch (Exception e) {
            System.err.println("测试失败: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    public static class TestObject {
        public ShipTypeEnum shipType;
        public String note;
    }
}

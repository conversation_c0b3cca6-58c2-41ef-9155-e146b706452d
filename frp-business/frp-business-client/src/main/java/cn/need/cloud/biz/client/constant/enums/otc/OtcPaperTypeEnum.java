package cn.need.cloud.biz.client.constant.enums.otc;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.AllArgsConstructor;
import lombok.Getter;


/***
 * OtcBuildShipPackageType.java
 *
 * <AUTHOR>
 * @since 2024-11-15
 */
@Getter
@AllArgsConstructor
public enum OtcPaperTypeEnum {

    /**
     * Label_4X6
     */
    LABEL_4X6("Label_4X6"),

    /**
     * A4
     */
    A4("A4");

    @EnumValue
    @JsonValue
    private final String type;

    /**
     * Jackson反序列化方法，用于将字符串转换为枚举
     *
     * @param type 枚举类型
     * @return OtcPaperTypeEnum 对应的枚举值，如果type为null或空字符串则返回null
     */
    @JsonCreator
    public static OtcPaperTypeEnum fromType(String type) {
        // 处理null或空字符串的情况
        if (type == null || type.trim().isEmpty()) {
            return null;
        }

        for (OtcPaperTypeEnum paperType : values()) {
            if (paperType.getType().equals(type)) {
                return paperType;
            }
        }
        throw new IllegalArgumentException("Unknown OtcPaperTypeEnum type: " + type);
    }
}

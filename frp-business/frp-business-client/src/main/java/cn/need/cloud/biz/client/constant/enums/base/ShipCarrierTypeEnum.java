package cn.need.cloud.biz.client.constant.enums.base;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.List;


/**
 * ShipCarrierTypeEnum枚举类用于定义不同的航运公司类型
 * 这个枚举类提供了一个标准化的方式来引用几家主要的航运公司
 *
 * <AUTHOR>
 */
@AllArgsConstructor
@Getter
public enum ShipCarrierTypeEnum {

    /**
     * FedEx航运公司
     */
    FEDEX("FedEx"),

    /**
     * Amazon航运公司
     */
    AMAZON("Amazon"),

    /**
     * OnTrac航运公司
     */
    ONTRAC("OnTrac"),

    /**
     * UPS航运公司
     */
    UPS("UPS");


    /**
     * 航运公司的类型名称
     */
    @EnumValue
    @JsonValue
    private final String type;

    /**
     * Jackson反序列化方法，用于将字符串转换为枚举
     *
     * @param type 枚举类型
     * @return ShipCarrierTypeEnum 对应的枚举值，如果type为null或空字符串则返回null
     */
    @JsonCreator
    public static ShipCarrierTypeEnum fromType(String type) {
        // 处理null或空字符串的情况
        if (type == null || type.trim().isEmpty()) {
            return null;
        }

        for (ShipCarrierTypeEnum carrierType : values()) {
            if (carrierType.getType().equals(type)) {
                return carrierType;
            }
        }
        throw new IllegalArgumentException("Unknown ShipCarrierTypeEnum type: " + type);
    }

    public static ShipCarrierTypeEnum typeOf(String type) {
        for (ShipCarrierTypeEnum statusEnum : values()) {
            if (statusEnum.getType().equals(type)) {
                return statusEnum;
            }
        }
        return null;
    }

    public static List<String> getStringTypeList() {
        return Arrays.stream(ShipCarrierTypeEnum.values()).map(ShipCarrierTypeEnum::getType).toList();
    }
}


package cn.need.cloud.biz.client.constant.enums.otb;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * Otb包裹label纸张类型枚举
 *
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum PaperTypeEnum {

    /**
     * 4x6 标签纸
     */
    LABEL_4X6("Label_4X6"),

    /**
     * 1x3 小标签纸
     */
    LABEL_1X3("Label_1X3"),

    /**
     * A4 打印纸
     */
    A4("A4"),

    /**
     * 按仓库配置选择纸张类型
     */
    BY_WAREHOUSE("ByWarehouse"),

    ;

    @EnumValue
    @JsonValue
    private final String status;

    /**
     * Jackson反序列化方法，用于将字符串转换为枚举
     *
     * @param status 枚举状态码
     * @return PaperTypeEnum 对应的枚举值，如果status为null或空字符串则返回null
     */
    @JsonCreator
    public static PaperTypeEnum fromStatus(String status) {
        // 处理null或空字符串的情况
        if (status == null || status.trim().isEmpty()) {
            return null;
        }

        for (PaperTypeEnum paperType : values()) {
            if (paperType.getStatus().equals(status)) {
                return paperType;
            }
        }
        throw new IllegalArgumentException("Unknown PaperTypeEnum status: " + status);
    }
}
